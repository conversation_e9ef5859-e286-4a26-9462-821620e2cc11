import javafx.application.Application;
import javafx.application.Platform;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

/**
 * 测试修复后的功能
 * 1. 测试托盘功能（英文菜单，双重事件处理）
 * 2. 测试浏览器启动（改进的用户数据目录）
 */
public class TestFixes extends Application {
    
    private Stage primaryStage;
    private SystemTray systemTray;
    private TrayIcon trayIcon;
    
    @Override
    public void start(Stage primaryStage) {
        this.primaryStage = primaryStage;
        
        VBox root = new VBox(10);
        
        Button trayTestButton = new Button("Test System Tray (English Menu)");
        trayTestButton.setOnAction(e -> testSystemTray());
        
        Button browserTestButton = new Button("Test Browser Launch (Unique UserData)");
        browserTestButton.setOnAction(e -> testBrowserLaunch());
        
        root.getChildren().addAll(trayTestButton, browserTestButton);
        
        Scene scene = new Scene(root, 400, 200);
        primaryStage.setTitle("Test Fixes - Tray & Browser");
        primaryStage.setScene(scene);
        primaryStage.show();
        
        primaryStage.setOnCloseRequest(event -> {
            event.consume();
            testSystemTray();
        });
    }
    
    private void testSystemTray() {
        System.out.println("=== Testing System Tray ===");
        
        if (!SystemTray.isSupported()) {
            System.err.println("System tray not supported");
            return;
        }
        
        primaryStage.hide();
        
        SwingUtilities.invokeLater(() -> {
            try {
                if (systemTray == null) {
                    systemTray = SystemTray.getSystemTray();
                }
                
                if (trayIcon != null) {
                    systemTray.remove(trayIcon);
                }
                
                // Create simple icon
                Image image = Toolkit.getDefaultToolkit().createImage(new byte[0]);
                
                // Create English popup menu
                PopupMenu popupMenu = new PopupMenu();
                MenuItem showItem = new MenuItem("Show Main Window");
                MenuItem exitItem = new MenuItem("Exit Application");
                
                showItem.addActionListener(e -> {
                    System.out.println("✓ Menu: Show Main Window clicked");
                    Platform.runLater(this::showWindow);
                });
                
                exitItem.addActionListener(e -> {
                    System.out.println("✓ Menu: Exit Application clicked");
                    Platform.runLater(this::exitApp);
                });
                
                popupMenu.add(showItem);
                popupMenu.addSeparator();
                popupMenu.add(exitItem);
                
                trayIcon = new TrayIcon(image, "Test App", popupMenu);
                trayIcon.setImageAutoSize(true);
                
                // Double event handling
                trayIcon.addActionListener(e -> {
                    System.out.println("✓ Tray icon double-clicked (ActionListener)");
                    Platform.runLater(this::showWindow);
                });
                
                trayIcon.addMouseListener(new MouseAdapter() {
                    @Override
                    public void mouseClicked(MouseEvent e) {
                        System.out.println("✓ Tray mouse event: clicks=" + e.getClickCount() + 
                                         ", button=" + e.getButton());
                        if (e.getClickCount() == 2 && e.getButton() == MouseEvent.BUTTON1) {
                            System.out.println("✓ Double-click detected (MouseListener)");
                            Platform.runLater(TestFixes.this::showWindow);
                        }
                    }
                });
                
                systemTray.add(trayIcon);
                System.out.println("✓ Tray icon added successfully");
                
                trayIcon.displayMessage("Test Success", 
                                      "English menu, double event handling works!", 
                                      TrayIcon.MessageType.INFO);
                
            } catch (Exception e) {
                System.err.println("✗ Tray test failed: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    private void testBrowserLaunch() {
        System.out.println("=== Testing Browser Launch ===");
        
        new Thread(() -> {
            try {
                // Test unique user data directory creation
                String userDataDir1 = createUniqueUserDataDir("Test1");
                String userDataDir2 = createUniqueUserDataDir("Test2");
                
                System.out.println("✓ UserData Dir 1: " + userDataDir1);
                System.out.println("✓ UserData Dir 2: " + userDataDir2);
                
                if (!userDataDir1.equals(userDataDir2)) {
                    System.out.println("✓ Unique user data directories created successfully");
                } else {
                    System.err.println("✗ User data directories are not unique");
                }
                
                // Test WebDriverManager reflection
                try {
                    Class<?> webDriverManagerClass = Class.forName("io.github.bonigarcia.wdm.WebDriverManager");
                    System.out.println("✓ WebDriverManager class found via reflection");
                    
                    Object edgeDriverManager = webDriverManagerClass.getMethod("edgedriver").invoke(null);
                    System.out.println("✓ EdgeDriver manager obtained");
                    
                    // Don't actually call setup() in test to avoid downloading drivers
                    System.out.println("✓ WebDriverManager reflection test passed");
                    
                } catch (Exception e) {
                    System.err.println("✗ WebDriverManager reflection failed: " + e.getMessage());
                }
                
            } catch (Exception e) {
                System.err.println("✗ Browser test failed: " + e.getMessage());
                e.printStackTrace();
            }
        }).start();
    }
    
    private String createUniqueUserDataDir(String prefix) {
        try {
            java.nio.file.Path tempDir = java.nio.file.Files.createTempDirectory(prefix + "_EdgeUserData_");
            tempDir.toFile().deleteOnExit();
            return tempDir.toAbsolutePath().toString();
        } catch (Exception e) {
            // Fallback
            return System.getProperty("java.io.tmpdir") + java.io.File.separator +
                   prefix + "_EdgeUserData_" + System.currentTimeMillis() + "_" + 
                   (int)(Math.random() * 10000);
        }
    }
    
    private void showWindow() {
        System.out.println("✓ Showing window...");
        try {
            if (primaryStage != null) {
                primaryStage.setIconified(false);
                primaryStage.show();
                primaryStage.toFront();
                primaryStage.requestFocus();
                
                primaryStage.setAlwaysOnTop(true);
                Platform.runLater(() -> primaryStage.setAlwaysOnTop(false));
                
                System.out.println("✓ Window shown successfully");
                
                SwingUtilities.invokeLater(() -> {
                    if (trayIcon != null && systemTray != null) {
                        try {
                            systemTray.remove(trayIcon);
                            trayIcon = null;
                            System.out.println("✓ Tray icon removed");
                        } catch (Exception e) {
                            System.err.println("✗ Failed to remove tray icon: " + e.getMessage());
                        }
                    }
                });
            }
        } catch (Exception e) {
            System.err.println("✗ Error showing window: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void exitApp() {
        System.out.println("✓ Exiting application...");
        try {
            if (trayIcon != null && systemTray != null) {
                systemTray.remove(trayIcon);
                System.out.println("✓ Tray icon removed");
            }
            Platform.exit();
            System.exit(0);
        } catch (Exception e) {
            System.err.println("✗ Error exiting app: " + e.getMessage());
            System.exit(1);
        }
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
